from RedisClientUS import redis_client
import json

q1 = [
    "您母亲的姓氏是什么?",
    "您的第一个/目前/最喜欢的宠物名称是什么?",
    "您的第一辆汽车是什么车?",
    "您在哪所小学上学?",
    "您出生的地方/城市叫什么名字?",
]
q2 = [
    "您成长的道路/街道叫称是什么?",
    "您最不喜欢的食物是什么?",
    "您的第一份工作是在哪家公司?",
    "您最喜欢的食物是什么?",
    "您在哪所高中求学?",
]
q3 = [
    "您在哪里遇见您的配偶?",
    "您的兄弟姐妹的中间名字是什么?",
    "谁是您儿时的英雄?",
    "您的第一份工作在哪个城市或地方?",
    "您申请但未就读的大学是哪所大学?",
]


def save_faker_user():
    # all_users_data = redis_client.hgetall("usUserDatas")
    # print(all_users_data)

    base_info = {
        "name": "谭健康",
        "username": "tanjiankang",
        "password": "Tan123456",
        "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "wang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "shanghai"}, {"DISP": "您在哪所高中求学?", "VAL": "erzhong"}],
        "visa_type": "B1/B2",
        "travel_date": "",
        "accept_nd": 0,
        "accept_vip": 0,
        "customer": "ceshi",
        "remark": "ceshi",
        "user_id": 10,
        "operator": "tanjiankang",
        "created_at": "2025-09-21",
        "status": "submitted",
        "auto_schedule": False,
    }
    users = [
        
        {
            "name": "吴嘉恒",
            "FullName": "",
            "username": "<EMAIL>",
            "password": "Wjh920702",
            "centerCode": "BEIJING",
            "visa_type": "",
            "start_date": "2025-10-01",
            "end_date": "2025-10-10",
            "qa": [
                {"DISP": "您母亲的姓氏是什么?", "VAL": "Wjh920702"},
                {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "SHANGHAI"},
                {"DISP": "您的第一份工作是在哪家公司?", "VAL": "ENACTUS"},
            ],
        },
    ]

    for u in users:
        new_user = {**base_info, **u}
        print(new_user)
        redis_client.hset("usUserDatas", u.get("username"), json.dumps(new_user))


def get_us_logs():
    us_datas = redis_client.hgetall("usDateInfos")
    vfs_data = []

    for us_data in us_datas:
        dates = [x.get("date") for x in us_data.get("dates")]
        vfs_data.append(
            {
                "country": "美国",
                "missionCode": "美国",
                "visaCategoryCode": us_data.get("visa_type"),
                "centerCode": us_data.get("visa_type"),
                "center": us_data.get("center_code"),
                "type": us_data.get("visa_type"),
                "date": dates,
                "update": us_data.get("update_time"),
            }
        )
    print(vfs_data)


if __name__ == "__main__":
    save_faker_user()
