from RedisClientUS import redis_client
import json


def save_faker_user():
    # all_users_data = redis_client.hgetall("usUserDatas")
    # print(all_users_data)

    base_info = {
        "name": "谭健康",
        "username": "tanjiankang",
        "password": "Tan123456",
        "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "wang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "shanghai"}, {"DISP": "您在哪所高中求学?", "VAL": "erzhong"}],
        "visa_type": "B1/B2",
        "travel_date": "",
        "accept_nd": 0,
        "accept_vip": 0,
        "customer": "ceshi",
        "remark": "ceshi",
        "user_id": 10,
        "operator": "tanjiankang",
        "created_at": "2025-09-21",
        "status": "submitted",
        "autoDate": False,
    }
    users = [
        {
            "name": "杨万苹",
            "FullName": "YANG WANPING",
            "username": "YANGWANPING1975",
            "password": "Sochange@2025",
            "centerCode": "GUANGZHOU",
            "visa_type": "B1/B2",
            "start_date": "2025-10-08",
            "end_date": "2025-10-15",
            "qa": [
                {"DISP": "您母亲的姓氏是什么?", "VAL": "Hu"},
                {"DISP": "您最喜欢的食物是什么?", "VAL": "RICE"},
                {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "FOSHAN"},
            ],
        },
        {
            "name": "熊越",
            "FullName": "XIONG YUE",
            "username": "XIONGYUE2001",
            "password": "Sochange@2025",
            "centerCode": "GUANGZHOU",
            "visa_type": "B1/B2",
            "start_date": "2025-10-08",
            "end_date": "2025-10-15",
            "qa": [
                {"DISP": "您母亲的姓氏是什么?", "VAL": "YANG"},
                {"DISP": "您最喜欢的食物是什么?", "VAL": "RICE"},
                {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "FOSHAN"},
            ],
        },
        {
            "name": "朱永",
            "FullName": "ZHUYONG",
            "username": "ZHUYONG1982",
            "password": "123456Qaz!",
            "centerCode": "SHANGHAI",
            "visa_type": "B1/B2",
            "qa": [
                {"DISP": "您母亲的姓氏是什么?", "VAL": "A"},
                {"DISP": "您在哪里遇见您的配偶?", "VAL": "C"},
                {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "B"},
            ],
        },
    ]

    for u in users:
        new_user = {**base_info, **u}
        print(new_user)
        redis_client.hset("usUserDatas", u.get("username"), json.dumps(new_user))


def get_us_logs():
    us_datas = redis_client.hgetall("usDateInfos")
    vfs_data = []

    for us_data in us_datas:
        dates = [x.get("date") for x in us_data.get("dates")]
        vfs_data.append(
            {
                "country": "美国",
                "missionCode": "美国",
                "visaCategoryCode": us_data.get("visa_type"),
                "centerCode": us_data.get("visa_type"),
                "center": us_data.get("center_code"),
                "type": us_data.get("visa_type"),
                "date": dates,
                "update": us_data.get("update_time"),
            }
        )
    print(vfs_data)


if __name__ == "__main__":
    save_faker_user()
