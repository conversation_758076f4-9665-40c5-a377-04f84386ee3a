from RedisClientUS import redis_client
import json

all_users_data = redis_client.hgetall("usUserDatas")
print(all_users_data)

base_info = {
    "name": "谭健康",
    "username": "tanjiankang",
    "password": "Tan123456",
    "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "wang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "shanghai"}, {"DISP": "您在哪所高中求学?", "VAL": "erzhong"}],
    "visa_type": [["usa", "US_DEFAULT", "TOURIST", "B1_B2"]],
    "start_date": "2025-09-24",
    "end_date": "2025-10-30",
    "travel_date": "",
    "accept_nd": 0,
    "accept_vip": 0,
    "customer": "ceshi",
    "remark": "ceshi",
    "user_id": 10,
    "operator": "tanjiankang",
    "created_at": "2025-09-21",
    "status": "submitted",
}
users = [
    {
        "name": "张菲菲",
        "username": "<EMAIL>",
        "password": "Zhang2024",
        "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "Zhang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "Shanghai"}, {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "Xinzhalu"}],
    },
    {
        "name": "周玉霞",
        "username": "<EMAIL>",
        "password": "Zhang2024",
        "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "Zhang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "Shanghai"}, {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "Xinzhalu"}],
    },
    {
        "name": "金枫",
        "username": "<EMAIL>",
        "password": "Zhang2024",
        "qa": [{"DISP": "您母亲的姓氏是什么?", "VAL": "Zhang"}, {"DISP": "您的第一份工作在哪个城市或地方?", "VAL": "Shanghai"}, {"DISP": "您成长的道路/街道叫称是什么?", "VAL": "Xinzhalu"}],
    }
]

for u in users:
    new_user = {**base_info, **u}
    print(new_user)
    redis_client.hset('usUserDatas', u.get('username'), json.dumps(new_user))
