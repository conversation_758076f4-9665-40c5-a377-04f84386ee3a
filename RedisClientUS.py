import redis
from tools.logger import logger
import json
# --- Redis 客户端定义 ---


class RedisClient:
    def __init__(self, host="127.0.0.1", port=6379, password=None, db=0):
        try:
            self.client = redis.Redis(
                host=host, port=port, password=password, db=db,
                decode_responses=True, retry_on_timeout=True,
            )
            self.client.ping()
            logger.info("成功连接到 Redis。")
        except redis.exceptions.ConnectionError as e:
            logger.error(f"无法连接到 Redis: {e}。请检查您的 Redis 服务和配置。")
            self.client = None

    # def hgetall(self, hash_name):
    #     if not self.client:
    #         return {}
    #     return self.client.hgetall(hash_name)
    
    def hgetall(self, hash_name):
        data = self.client.hgetall(hash_name)
        json_array = []
        for field, value in data.items():
            try:
                json_object = json.loads(value)
                json_array.append(json_object)
            except json.JSONDecodeError:
                pass  # 如果解析失败，则跳过这个值
        return json_array

    def hset(self, hash_name, field, value):
        if not self.client:
            return None
        return self.client.hset(hash_name, field, value)
    
    def hget(self, hash_name, field):
        if not self.client:
            return None
        return self.client.hget(hash_name, field)

    def hdel(self, hash_name, *fields):
        if not self.client:
            return None
        return self.client.hdel(hash_name, *fields)

    def set(self, key, value, expire_in_seconds=None):
        if not self.client:
            return None
        return self.client.set(key, value, ex=expire_in_seconds)

    def get(self, key):
        if not self.client:
            return None
        return self.client.get(key)


# --- 初始化 ---*************  ***********
redis_client = RedisClient(host="*************", password="TicketsCache#2023")
