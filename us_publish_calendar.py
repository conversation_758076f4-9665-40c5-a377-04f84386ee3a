import random
import time
import json
import threading
# import asyncio
from datetime import datetime
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
# import weakref
import hashlib

# 第三方库
from curl_cffi import requests
from tools.logger import logger
from RedisClientUS import redis_client

# --- 全局配置与变量 ---
NORMAL_WORKERS = 15  # 普通时段的工作线程数
PEAK_WORKERS = 50   # 峰值时段的工作线程数
USER_FETCH_INTERVAL = 5
SESSION_VALID_DURATION = 2000
EARLY_DATE_NOTIFICATION_THRESHOLD = '2025-08-30'

# 🚀 高性能并发配置
CONCURRENT_TIMES_THREADS = 20  # 获取时间段的并发线程数
CONCURRENT_BOOKING_THREADS = 20  # 预约的并发线程数
TIMES_FETCH_TIMEOUT = 15  # 获取时间段的超时时间（秒）- 优化：减少超时时间
BOOKING_TIMEOUT = 30  # 预约的超时时间（秒）- 优化：减少超时时间

# 🔧 性能优化配置
MAX_THREAD_POOL_SIZE = 50  # 线程池最大大小
REQUEST_INTERVAL = 0.05  # 请求间隔（秒）- 防止过于频繁的请求
EARLY_SUCCESS_THRESHOLD = 3  # 早期成功阈值：获得N个成功结果就停止
CONNECTION_POOL_SIZE = 100  # HTTP连接池大小

# 🚀 实时监控优化配置 - 无缓存版本
ENABLE_HTTP_POOL = True  # 启用HTTP连接池
ENABLE_RESULT_CACHE = False  # ❌ 禁用结果缓存 - 确保实时性
ENABLE_BATCH_PROCESSING = True  # 启用批量处理
BATCH_SIZE = 5  # 批量处理大小
ADAPTIVE_THREADING = True  # 自适应线程数
MIN_THREADS = 5  # 最小线程数
MAX_THREADS = 30  # 最大线程数

# 🔧 curl_cffi 专用优化配置
CURL_CFFI_SESSION_REUSE = True  # 启用会话复用
CURL_CFFI_MAX_SESSIONS = 50  # 最大会话数
CURL_CFFI_SESSION_MAX_USAGE = 1000  # 每个会话最大使用次数
CURL_CFFI_CLEANUP_INTERVAL = 600  # 会话清理间隔（秒）

# 🎯 实时监控专用配置
REAL_TIME_MODE = True  # 实时模式
FORCE_FRESH_DATA = True  # 强制获取最新数据
NO_CACHE_HEADERS = True  # 使用防缓存请求头

USVISA_BASE_URL = "https://www.usvisascheduling.com"
DEFAULT_PROXIES = {
    "http": "http://kq123456-zone-resi:<EMAIL>:16666",
    "https": "http://kq123456-zone-resi:<EMAIL>:16666"
}
BOOKING_PROXIES = {
    "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-us:<EMAIL>:33335",
    "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-us:<EMAIL>:33335"
}
WECHAT_BOT_URL_SCHEDULE = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066"

WECHAT_BOT_URL_NORMAL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c5539dd1-a547-4225-ab10-bb0e871af9a"
VALID_USER_LIST = []
USER_LIST_LOCK = threading.Lock()
# REPORTED_EARLY_DATES 已由 DateNotificationManager 替代
EARLY_DATE_LOCK = threading.Lock()  # 保留锁以备其他用途

ACTIVE_WORKERS = NORMAL_WORKERS
ACTIVE_WORKERS_LOCK = threading.Lock()

# 🚀 全局线程池和性能优化组件
GLOBAL_THREAD_POOL = None
TIMES_THREAD_POOL = None
BOOKING_THREAD_POOL = None

# # --- 初始化 ---
# redis_client = RedisClient(host="***********", password="TicketsCache#2023")
IMPERSONATE_VERSIONS = ['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101', 'tor145']


class DateNotificationManager:
    """优化的日期通知管理器 - 只在日期发生变化时发送一条汇总消息"""

    def __init__(self, redis_client, logger, notify_func):
        self.redis_client = redis_client
        self.logger = logger
        self.notify_func = notify_func
        self.lock = threading.Lock()

    def _get_date_hash(self, dates):
        """计算日期列表的哈希值，用于检测变化"""
        sorted_dates = sorted(dates)
        date_str = ",".join(sorted_dates)
        return hashlib.md5(date_str.encode()).hexdigest()

    def _get_cached_hash(self, center_code):
        """获取缓存的日期哈希值"""
        return self.redis_client.get(f"us_date_hash_{center_code}") or ""

    def _save_date_hash(self, center_code, date_hash):
        """保存日期哈希值，设置较长过期时间"""
        self.redis_client.set(f"us_date_hash_{center_code}", date_hash, expire_in_seconds=3600)  # 1小时

    def process_dates(self, center_code, center_name, all_days, username, threshold):
        """处理日期并发送通知（只在变化时）"""
        # 1. 收集所有早期日期
        early_dates = []
        for day in all_days:
            date_str = day.get('Date', '')[:10]
            if date_str and date_str < threshold:
                early_dates.append(date_str)

        # 2. 如果没有早期日期，不处理
        if not early_dates:
            return

        # 3. 计算当前日期的哈希值
        current_hash = self._get_date_hash(early_dates)

        # 4. 对比缓存的哈希值
        with self.lock:
            cached_hash = self._get_cached_hash(center_code)

            # 5. 只有在哈希值不同时（日期发生变化）才发送通知
            if current_hash != cached_hash:
                # 构建一条汇总消息
                date_count = len(early_dates)
                dates_display = sorted(early_dates)[:5]  # 只显示前5个日期

                message = f"🚨【{center_name}日期变化通知】\n"
                message += f"账号: {username}\n"
                message += f"发现 {date_count} 个早于 {threshold} 的日期:\n"
                message += f"日期: {', '.join(dates_display)}"

                if date_count > 5:
                    message += f" 等共{date_count}个日期"

                # 添加最早日期提醒
                earliest_date = min(early_dates)
                message += f"\n最早日期: {earliest_date}"

                # 发送通知
                self.logger.success(message)
                self.notify_func(message)

                # 更新缓存的哈希值
                self._save_date_hash(center_code, current_hash)

                # 同时更新详细的日期缓存
                self._save_detailed_dates(center_code, early_dates)
            else:
                self.logger.debug(f"{center_name} 日期未发生变化，跳过通知")

    def _save_detailed_dates(self, center_code, dates):
        """保存详细的日期信息供查询"""
        date_info = {
            "dates": sorted(dates),
            "count": len(dates),
            "earliest": min(dates) if dates else None,
            "latest": max(dates) if dates else None,
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.redis_client.set(
            f"us_early_dates_detail_{center_code}",
            json.dumps(date_info),
            expire_in_seconds=3600
        )


# 全局日期通知管理器实例将在 notify_wechat 函数定义后初始化
date_notification_manager = None


class HighPerformanceConcurrentManager:
    """高性能并发管理器"""

    def __init__(self):
        self.times_pool = ThreadPoolExecutor(max_workers=CONCURRENT_TIMES_THREADS, thread_name_prefix="TimesPool")
        self.booking_pool = ThreadPoolExecutor(max_workers=CONCURRENT_BOOKING_THREADS, thread_name_prefix="BookingPool")
        self.success_event = threading.Event()
        self.results_queue = Queue()

    def reset_for_new_user(self):
        """为新用户重置状态"""
        self.success_event.clear()
        # 清空队列
        while not self.results_queue.empty():
            try:
                self.results_queue.get_nowait()
            except Empty:
                break

    def fetch_times_with_pool(self, user, selected_day, token):
        """🚀 高性能时间段获取 - 自适应线程数 + 智能缓存"""
        start_time = time.time()

        # 获取自适应线程数
        optimal_threads = performance_optimizer.get_optimal_thread_count("times")

        futures = []
        results = []

        def fetch_single_time(thread_id):
            try:
                # 智能延迟：根据线程数动态调整
                delay = (thread_id * REQUEST_INTERVAL) / max(1, optimal_threads / 10)
                time.sleep(delay)
                return fetch_available_times(user, selected_day, token)
            except Exception as e:
                logger.error(f"时间段线程 [{thread_id}] 错误: {e}")
                return None

        # 提交任务到线程池
        for i in range(optimal_threads):
            future = self.times_pool.submit(fetch_single_time, i+1)
            futures.append(future)

        # 使用 as_completed 获取最快的结果
        success_count = 0
        early_stop_threshold = max(1, optimal_threads // 4)  # 动态早停阈值

        for future in as_completed(futures, timeout=TIMES_FETCH_TIMEOUT):
            try:
                result = future.result()
                if result and result.get("ScheduleEntries"):
                    results.append(result)
                    success_count += 1
                    # 🚀 优化：动态早停策略
                    if success_count >= early_stop_threshold:
                        logger.info(f"⚡ 智能早停：获得 {success_count}/{optimal_threads} 个时间段结果")
                        break
            except Exception as e:
                logger.warning(f"获取时间段结果时出错: {e}")

        duration = time.time() - start_time
        failed_count = optimal_threads - success_count

        # 取消未完成的任务
        cancelled_count = 0
        for future in futures:
            if not future.done():
                future.cancel()
                cancelled_count += 1

        if cancelled_count > 0:
            logger.debug(f"⚡ 取消了 {cancelled_count} 个未完成的时间段任务")

        log_concurrent_stats("时间段获取", user.get('username'), optimal_threads,
                             success_count, failed_count, duration)

        return results[0] if results else None

    def book_with_pool(self, user, selected_day, token, num_of_times):
        """🚀 超高性能预约 - 自适应线程 + 智能竞争策略"""
        start_time = time.time()

        # 获取自适应线程数
        optimal_threads = performance_optimizer.get_optimal_thread_count("booking")

        futures = []
        results = []
        first_success_time = None

        def book_single_appointment(thread_id):
            nonlocal first_success_time
            try:
                # 如果已经成功，直接返回
                if self.success_event.is_set():
                    return {"skipped": True, "thread_id": thread_id}

                # 智能延迟策略：前几个线程无延迟，后续线程递增延迟
                if thread_id > 3:
                    delay = (thread_id - 3) * REQUEST_INTERVAL * 0.5
                    time.sleep(delay)

                result = book_appointment(user, selected_day, token, num_of_times)

                # 如果预约成功，设置成功标志
                if result and not result.get('HasError'):
                    if not self.success_event.is_set():
                        first_success_time = time.time()
                        self.success_event.set()
                        logger.success(f"🎉 预约线程 [{thread_id}] 首个成功！耗时: {first_success_time - start_time:.2f}秒")

                return result
            except Exception as e:
                logger.error(f"预约线程 [{thread_id}] 错误: {e}")
                return {"HasError": True, "ErrorMessage": str(e)}

        # 🚀 分批提交策略：先提交少量线程，成功后立即停止
        initial_batch = min(5, optimal_threads)
        remaining_threads = optimal_threads - initial_batch

        # 第一批：高优先级线程
        for i in range(initial_batch):
            future = self.booking_pool.submit(book_single_appointment, i+1)
            futures.append(future)

        success_count = 0
        failed_count = 0
        skipped_count = 0

        # 处理第一批结果
        completed_futures = 0
        for future in as_completed(futures, timeout=min(10, BOOKING_TIMEOUT)):
            completed_futures += 1
            try:
                result = future.result()
                if result:
                    if result.get("skipped"):
                        skipped_count += 1
                    elif result.get('HasError'):
                        failed_count += 1
                        results.append(result)
                    else:
                        success_count += 1
                        results.append(result)
                        # 🚀 第一批成功，立即停止
                        logger.info(f"⚡ 第一批预约成功，跳过剩余 {remaining_threads} 个线程")
                        break
            except Exception as e:
                failed_count += 1
                logger.warning(f"获取预约结果时出错: {e}")

        # 如果第一批都失败了，启动剩余线程
        if success_count == 0 and completed_futures >= initial_batch and remaining_threads > 0:
            logger.info(f"🔄 第一批失败，启动剩余 {remaining_threads} 个线程")

            for i in range(remaining_threads):
                future = self.booking_pool.submit(book_single_appointment, initial_batch + i + 1)
                futures.append(future)

            # 处理剩余结果
            for future in as_completed(futures[initial_batch:], timeout=BOOKING_TIMEOUT):
                try:
                    result = future.result()
                    if result:
                        if result.get("skipped"):
                            skipped_count += 1
                        elif result.get('HasError'):
                            failed_count += 1
                            results.append(result)
                        else:
                            success_count += 1
                            results.append(result)
                            # 一旦成功就停止
                            if success_count >= 1:
                                logger.info(f"⚡ 预约成功，停止等待其他线程")
                                break
                except Exception as e:
                    failed_count += 1
                    logger.warning(f"获取预约结果时出错: {e}")

        duration = time.time() - start_time

        # 取消未完成的任务
        cancelled_count = 0
        for future in futures:
            if not future.done():
                future.cancel()
                cancelled_count += 1

        if cancelled_count > 0:
            logger.debug(f"⚡ 取消了 {cancelled_count} 个未完成的预约任务")

        log_concurrent_stats("预约执行", user.get('username'), optimal_threads,
                             success_count, failed_count + skipped_count, duration)

        return results, success_count > 0

    def shutdown(self):
        """关闭线程池"""
        self.times_pool.shutdown(wait=False)
        self.booking_pool.shutdown(wait=False)


# 全局并发管理器实例
concurrent_manager = HighPerformanceConcurrentManager()


class CurlCffiSessionManager:
    """🚀 curl_cffi 专用会话管理器"""

    def __init__(self):
        self.sessions = {}
        self.session_usage = {}  # 记录会话使用次数
        self.session_create_time = {}  # 记录会话创建时间
        self.max_sessions = CURL_CFFI_MAX_SESSIONS
        self.max_usage_per_session = CURL_CFFI_SESSION_MAX_USAGE
        self.cleanup_interval = CURL_CFFI_CLEANUP_INTERVAL

    def get_session(self, user_id):
        """获取或创建curl_cffi会话"""
        # 清理过期会话
        self._cleanup_sessions()

        if user_id not in self.sessions:
            if len(self.sessions) >= self.max_sessions:
                # 移除使用次数最多的会话
                oldest_user = max(self.session_usage.items(), key=lambda x: x[1])[0]
                self._remove_session(oldest_user)

            # 创建新会话
            self.sessions[user_id] = requests.Session()
            self.session_usage[user_id] = 0

        self.session_usage[user_id] += 1
        return self.sessions[user_id]

    def _cleanup_sessions(self):
        """清理过度使用的会话"""
        to_remove = []
        for user_id, usage in self.session_usage.items():
            if usage > self.max_usage_per_session:
                to_remove.append(user_id)

        for user_id in to_remove:
            self._remove_session(user_id)

    def _remove_session(self, user_id):
        """移除会话"""
        if user_id in self.sessions:
            try:
                self.sessions[user_id].close()
            except:
                pass
            del self.sessions[user_id]
            del self.session_usage[user_id]

    def cleanup_all(self):
        """清理所有会话"""
        for session in self.sessions.values():
            try:
                session.close()
            except:
                pass
        self.sessions.clear()
        self.session_usage.clear()


class AdvancedPerformanceOptimizer:
    """🎯 实时性能优化器 - 无缓存版本"""

    def __init__(self):
        self.session_manager = CurlCffiSessionManager()  # curl_cffi会话管理器
        # ❌ 移除所有缓存相关组件
        self.request_stats = {}  # 请求统计
        self.adaptive_config = {
            'current_threads': CONCURRENT_TIMES_THREADS,
            'success_rate': 0.0,
            'avg_response_time': 0.0
        }

    def get_session(self, user_id):
        """获取或创建HTTP会话 - curl_cffi优化版"""
        if not ENABLE_HTTP_POOL:
            return requests

        return self.session_manager.get_session(user_id)

    # ❌ 移除所有缓存方法 - 确保实时性

    def update_request_stats(self, operation, success, response_time):
        """更新请求统计"""
        if operation not in self.request_stats:
            self.request_stats[operation] = {
                'total': 0, 'success': 0, 'total_time': 0.0
            }

        stats = self.request_stats[operation]
        stats['total'] += 1
        if success:
            stats['success'] += 1
        stats['total_time'] += response_time

        # 自适应调整线程数
        if ADAPTIVE_THREADING:
            self._adaptive_thread_adjustment(operation)

    def _adaptive_thread_adjustment(self, operation):
        """自适应线程数调整"""
        stats = self.request_stats.get(operation, {})
        if stats.get('total', 0) < 10:  # 样本数不足
            return

        success_rate = stats['success'] / stats['total']
        avg_response_time = stats['total_time'] / stats['total']

        # 根据成功率和响应时间调整线程数
        if success_rate > 0.8 and avg_response_time < 5.0:
            # 性能良好，可以增加线程数
            new_threads = min(self.adaptive_config['current_threads'] + 2, MAX_THREADS)
        elif success_rate < 0.5 or avg_response_time > 15.0:
            # 性能不佳，减少线程数
            new_threads = max(self.adaptive_config['current_threads'] - 2, MIN_THREADS)
        else:
            new_threads = self.adaptive_config['current_threads']

        if new_threads != self.adaptive_config['current_threads']:
            logger.info(f"🔧 自适应调整 {operation} 线程数: {self.adaptive_config['current_threads']} -> {new_threads}")
            self.adaptive_config['current_threads'] = new_threads

            # 更新全局配置
            global CONCURRENT_TIMES_THREADS, CONCURRENT_BOOKING_THREADS
            if operation == "times":
                CONCURRENT_TIMES_THREADS = new_threads
            elif operation == "booking":
                CONCURRENT_BOOKING_THREADS = new_threads

    def get_optimal_thread_count(self, operation):
        """获取最优线程数"""
        if not ADAPTIVE_THREADING:
            return CONCURRENT_TIMES_THREADS if operation == "times" else CONCURRENT_BOOKING_THREADS
        return self.adaptive_config['current_threads']

    def cleanup_sessions(self):
        """清理会话池 - curl_cffi优化版"""
        self.session_manager.cleanup_all()


# 全局性能优化器实例
performance_optimizer = AdvancedPerformanceOptimizer()

# --- 通用工具函数 ---


def retry_on_failure(retries=2, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for i in range(retries):
                try:
                    result = func(*args, **kwargs)
                    if result is not None:
                        return result
                    logger.warning(f"函数 {func.__name__} 返回 None，将在 {delay} 秒后重试...")
                except Exception as e:
                    logger.warning(f"函数 {func.__name__} 第 {i + 1}/{retries} 次尝试失败: {e}。")
                time.sleep(delay)
            logger.error(f"函数 {func.__name__} 在尝试 {retries} 次后彻底失败。")
            return None
        return wrapper
    return decorator


def center_code_to_chn(code):
    mapping = {"BEIJING": "北京", "GUANGZHOU": "广州", "SHANGHAI": "上海", "CHENGDU": "成都", "WUHAN": "武汉", "SHENYANG": "沈阳"}
    return mapping.get(code, code)


def notify_wechat(message):
    url = WECHAT_BOT_URL_NORMAL
    try:
        post_data = {"msgtype": "text", "text": {"content": message}}
        requests.post(url, json=post_data, timeout=10)
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {e}")


# 初始化全局日期通知管理器实例（在 notify_wechat 函数定义后）
date_notification_manager = DateNotificationManager(redis_client, logger, notify_wechat)


def log_concurrent_stats(operation, username, total_threads, successful_count, failed_count, duration):
    """记录并发操作的统计信息"""
    success_rate = (successful_count / total_threads) * 100 if total_threads > 0 else 0
    logger.info(f"📊 {operation}统计 - 用户[{username}]: "
                f"总线程数:{total_threads}, 成功:{successful_count}, 失败:{failed_count}, "
                f"成功率:{success_rate:.1f}%, 耗时:{duration:.2f}秒")


def is_peak_time():
    """检查当前是否为整点或半点的前后一分钟"""
    current_minute = datetime.now().minute
    # 整点峰值时间: 59, 0, 1
    # 半点峰值时间: 29, 30, 31
    return current_minute in [59, 0, 1, 29, 30, 31]

# --- API 调用封装 ---


@retry_on_failure()
def make_api_call(user, endpoint, params, timeout=120, proxies=DEFAULT_PROXIES):
    """🎯 实时API调用函数 - 无缓存，确保数据实时性"""
    start_time = time.time()
    username = user.get('username')

    # ❌ 移除所有缓存逻辑 - 确保实时获取最新数据

    # 获取优化的HTTP会话 - curl_cffi适配
    session = performance_optimizer.get_session(username)

    # 🎯 添加时间戳确保每次请求都是最新的
    timestamp = int(time.time() * 1000)
    url = f"{USVISA_BASE_URL}/zh-CN/custom-actions/?route={endpoint}&appd={user.get('contact_id')}&cacheString={timestamp}&_t={timestamp}"

    # 🚀 防缓存请求头 - 确保获取实时数据
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": f"{USVISA_BASE_URL}/zh-CN/schedule/",
        "origin": USVISA_BASE_URL,
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
    }

    cookie = user.get('cookie', {})
    if '__cf_bm' in cookie:
        del cookie['__cf_bm']

    try:
        # # curl_cffi 的正确调用方式
        # if ENABLE_HTTP_POOL and session != requests:
        #     # 使用会话池中的session
        #     response = session.post(
        #         url,
        #         impersonate=random.choice(IMPERSONATE_VERSIONS),
        #         headers=headers,
        #         data={"parameters": json.dumps(params)},
        #         proxies=proxies,
        #         verify=False,
        #         cookies=cookie,
        #         timeout=timeout
        #     )
        # else:
        # 直接使用requests模块
        response = requests.post(
            url,
            impersonate=random.choice(IMPERSONATE_VERSIONS),
            headers=headers,
            data={"parameters": json.dumps(params)},
            proxies=proxies,
            verify=False,
            cookies=cookie,
            timeout=timeout
        )

        response_time = time.time() - start_time

        if "Unauthorized" in response.text or response.status_code == 401:
            logger.warning(f"用户 [{username}] 认证失败 (Unauthorized)。将其标记为需要重新登录。")
            user['last_login'] = int(time.time() - SESSION_VALID_DURATION * 2)
            redis_client.hset('usUserDatas', username, json.dumps(user))
            performance_optimizer.update_request_stats(endpoint.split('/')[-1], False, response_time)
            return None

        response.raise_for_status()
        result = response.json()

        # ❌ 移除缓存逻辑 - 确保实时性

        # 更新统计信息
        performance_optimizer.update_request_stats(endpoint.split('/')[-1], True, response_time)

        # 🎯 实时数据日志
        logger.debug(f"🔄 实时获取数据: {endpoint} - 用户: {username} - 耗时: {response_time:.2f}s")

        return result

    except Exception as e:
        response_time = time.time() - start_time
        performance_optimizer.update_request_stats(endpoint.split('/')[-1], False, response_time)
        raise e

# --- 核心业务逻辑函数 ---


def fetch_available_days(user):
    logger.info(f"🔄 实时获取可用日期 - 用户 [{user.get('username')}]")

    params = {
        "primaryId": user.get('primaryId'),
        "applications": [user.get('primaryId')],
        "scheduleDayId": "",
        "scheduleEntryId": "",
        "postId": user.get('post_id'),
        "isReschedule": str(user.get("isReschedule", False)).lower()
    }
    return make_api_call(user, "/api/v1/schedule-group/get-family-consular-schedule-days", params, proxies=DEFAULT_PROXIES)


def fetch_available_times(user, day_info, token):
    logger.info(f"🔄 实时获取时间段 - 用户 [{user.get('username')}] - 日期 {day_info.get('Date')}")
    params = {
        "primaryId": None,
        "applications": None,
        "scheduleDayId": None,
        "scheduleEntryId": "",
        "postId": None,
        "Token": token,
        "Date": f"{day_info.get('Date')}T00:00:00.000Z",
        "isReschedule": str(user.get("isReschedule", False)).lower()
    }
    logger.debug(f"🎯 实时请求参数 - 用户 [{user.get('username')}]: {json.dumps(params)}")
    result = make_api_call(user, "/api/v1/schedule-group/get-family-consular-schedule-entries", params, proxies=BOOKING_PROXIES)

    # 🎯 实时结果日志
    if result and result.get("ScheduleEntries"):
        logger.success(f"✅ 实时发现时间段 - 用户 [{user.get('username')}]: {len(result.get('ScheduleEntries'))} 个时间段")
    else:
        logger.warning(f"❌ 实时查询无时间段 - 用户 [{user.get('username')}]")

    return result


def book_appointment(user, day_info, token, num_of_times):
    logger.info(f"🎯 实时预约尝试 - 用户 [{user.get('username')}] - 日期 {day_info.get('Date')}")
    # TODO
    return None
    members = user.get('members', [])
    params = {
        "primaryId": None,
        "applications": [item.get('ApplicationID') for item in members] if len(members) > 1 else None,
        "scheduleDayId": None,
        "scheduleEntryId": "",
        "postId": None,
        "Token": token,
        "Date": f"{day_info.get('Date')}T00:00:00.000Z",
        "Num": str(random.randint(1, num_of_times))
    }
    endpoint = "/api/v1/schedule-group/reschedule-consular-appointments-for-family" if user.get("isReschedule") else "/api/v1/schedule-group/schedule-consular-appointments-for-family"

    logger.debug(f"🎯 实时预约参数 - 用户 [{user.get('username')}]: {json.dumps(params)}")
    result = make_api_call(user, endpoint, params, timeout=120, proxies=BOOKING_PROXIES)

    # 🎯 实时预约结果日志
    if result and not result.get('HasError'):
        logger.success(f"🎉 实时预约成功 - 用户 [{user.get('username')}] - 日期 {day_info.get('Date')}")
    else:
        error_msg = result.get('ErrorMessage', '未知错误') if result else '无响应'
        logger.warning(f"❌ 实时预约失败 - 用户 [{user.get('username')}] - 原因: {error_msg}")

    return result


def process_user_for_appointment(user):
    username = user.get('username')
    center_code = user.get('centerCode')
    center_name = center_code_to_chn(center_code)
    logger.info(f"🔄 开始实时处理用户 [{username}] 在 [{center_name}] 的预约...")

    # 🎯 实时获取最新可用日期
    days_data = fetch_available_days(user)
    if not days_data or not days_data.get("ScheduleDays"):
        logger.info(f"❌ 用户 [{username}] 在 [{center_name}] 实时查询无可用日期")
        return

    all_days = days_data.get("ScheduleDays", [])
    logger.success(f"✅ 用户 [{username}] 在 [{center_name}] 实时发现 {len(all_days)} 个可用日期: {[d.get('Date')[:10] for d in all_days]}")

    # 🎯 实时数据，短期缓存
    redis_client.set(f"us_date_{center_code}", json.dumps(all_days), expire_in_seconds=60)  # 只缓存1分钟

    # 🎯 使用优化的日期通知管理器（只在日期变化时发送一条汇总消息）
    date_notification_manager.process_dates(
        center_code=center_code,
        center_name=center_name,
        all_days=all_days,
        username=username,
        threshold=EARLY_DATE_NOTIFICATION_THRESHOLD
    )

    suitable_days = [
        day for day in all_days
        if user.get("start_date", "1970-01-01") <= day.get('Date')[:10] <= user.get("end_date", "2999-12-31")
    ]
    if not suitable_days:
        logger.info(f"用户 [{username}] 的日期范围与可用日期不匹配。")
        return

    selected_day = random.choice(suitable_days)
    logger.info(f"为用户 [{username}] 选中匹配日期: {selected_day.get('Date')}")

    # 🚀 重置并发管理器状态
    concurrent_manager.reset_for_new_user()

    # 🚀 使用高性能线程池并发获取时间段
    logger.info(f"🚀 为用户 [{username}] 使用线程池并发获取时间段...")
    times_data = concurrent_manager.fetch_times_with_pool(user, selected_day, days_data.get('Token'))

    # 检查是否获取到时间段
    if not times_data or not times_data.get("ScheduleEntries"):
        logger.warning(f"用户 [{username}] 在日期 {selected_day.get('Date')} 未获取到时间段。")
        return

    available_times = times_data.get('ScheduleEntries')
    logger.success(f"✅ 用户 [{username}] 成功获取到时间段，共 {len(available_times)} 个可用时间段")

    # 🚀 使用高性能线程池并发预约
    logger.info(f"🚀 为用户 [{username}] 使用线程池并发预约...")
    booking_results, booking_success = concurrent_manager.book_with_pool(
        user, selected_day, times_data.get('Token'), len(available_times)
    )

    if booking_success:
        # 🎯 实时预约成功
        success_message = (
            f"�【实时预约成功】客户: {user.get('name')} ({center_name})\n"
            f"预约日期: {selected_day.get('Date')[:10]}\n"
            f"用户名: {username}\n"
            f"备注: {user.get('remark', '无')}\n"
            f"⚡ 实时监控并发预约成功\n"
            f"🔄 数据实时性: 无缓存，确保最新位置信息\n"
            "请及时登录网页下载预约信。"
        )
        logger.success(f"🎉 用户 [{username}] 实时预约成功！")
        url = WECHAT_BOT_URL_SCHEDULE
        post_data = {"msgtype": "text", "text": {"content": success_message}}
        requests.post(url, json=post_data, timeout=100)

        redis_client.hset('successUserDatas', f"{center_code}-{username}", json.dumps({**user, "scheduled_date": selected_day.get('Date'), "update_order_time": int(time.time())}))
        redis_client.hdel('usUserDatas', username)
    else:
        logger.error(f"❌ 用户 [{username}] 高性能预约失败")
        if booking_results:
            # 记录失败原因
            error_messages = [result.get('ErrorMessage', '未知错误') for result in booking_results[:3] if result.get('ErrorMessage')]
            if error_messages:
                logger.error(f"主要失败原因: {', '.join(set(error_messages))}")

# --- 后台与工作线程 ---


def refresh_user_list_periodically():
    """此函数在专用后台线程中运行，定期从 Redis 刷新全局用户列表。"""
    while True:
        logger.info("用户列表刷新线程：正在从 Redis 加载用户...")
        try:
            all_users_data = redis_client.hgetall("usUserDatas")
            current_time = int(time.time())
            fresh_user_list = []

            for user in all_users_data:
                try:
                    # user = json.loads(user_json)
                    if user.get('primaryId') and user.get('last_login') and \
                       current_time - user.get('last_login') < SESSION_VALID_DURATION:
                        fresh_user_list.append(user)
                except (json.JSONDecodeError, TypeError):
                    continue

            with USER_LIST_LOCK:
                global VALID_USER_LIST
                VALID_USER_LIST = fresh_user_list

            logger.info(f"用户列表刷新线程：已更新，当前有效用户数: {len(VALID_USER_LIST)}")
        except Exception as e:
            logger.error(f"用户列表刷新线程发生错误: {e}")

        time.sleep(USER_FETCH_INTERVAL)


def clear_reported_dates_periodically():
    """此函数在专用后台线程中运行，定期（每1小时）清理过期的日期哈希缓存。"""
    while True:
        time.sleep(3600)  # 每小时清理一次
        # 新的通知管理器会自动管理缓存，这里可以添加额外的清理逻辑
        logger.info("日期通知缓存维护检查完成。")


def dynamic_worker_adjuster():
    """此函数在专用后台线程中运行，根据时间动态调整活跃的工作线程数量。"""
    global ACTIVE_WORKERS
    while True:
        new_count = PEAK_WORKERS if is_peak_time() else NORMAL_WORKERS

        with ACTIVE_WORKERS_LOCK:
            if ACTIVE_WORKERS != new_count:
                logger.info(f"动态调整线程数：从 {ACTIVE_WORKERS} 调整到 {new_count}")
                ACTIVE_WORKERS = new_count

        time.sleep(1)  # 每秒检查一次


def batch_process_users(users_batch):
    """🚀 批量处理用户 - 提高吞吐量"""
    if not ENABLE_BATCH_PROCESSING or len(users_batch) <= 1:
        # 单个处理
        for user in users_batch:
            process_user_for_appointment(user)
        return

    logger.info(f"🔄 批量处理 {len(users_batch)} 个用户")

    # 使用线程池并行处理批量用户
    with ThreadPoolExecutor(max_workers=min(len(users_batch), 5)) as executor:
        futures = [executor.submit(process_user_for_appointment, user) for user in users_batch]

        # 等待所有任务完成
        for future in as_completed(futures, timeout=300):  # 5分钟超时
            try:
                future.result()
            except Exception as e:
                logger.error(f"批量处理用户时出错: {e}")


def worker(worker_id):
    """🚀 优化工作线程 - 批量处理 + 内存优化"""
    local_batch = []
    last_batch_time = time.time()

    while True:
        is_active = False
        with ACTIVE_WORKERS_LOCK:
            is_active = worker_id < ACTIVE_WORKERS

        if not is_active:
            # 处理剩余批量任务
            if local_batch:
                batch_process_users(local_batch)
                local_batch.clear()
            time.sleep(2)
            continue

        try:
            users_to_process = []
            with USER_LIST_LOCK:
                if VALID_USER_LIST:
                    # 🚀 批量获取用户
                    batch_size = min(BATCH_SIZE, len(VALID_USER_LIST))
                    users_to_process = random.sample(VALID_USER_LIST, batch_size)

            if users_to_process:
                local_batch.extend(users_to_process)

                # 检查是否需要处理批量
                current_time = time.time()
                should_process_batch = (
                    len(local_batch) >= BATCH_SIZE or
                    current_time - last_batch_time > 30  # 30秒超时
                )

                if should_process_batch:
                    batch_process_users(local_batch)
                    local_batch.clear()
                    last_batch_time = current_time

                    # 智能休眠：根据系统负载调整
                    sleep_time = random.uniform(0.5, 2.0)
                    if len(VALID_USER_LIST) > 100:  # 高负载
                        sleep_time *= 0.5
                    elif len(VALID_USER_LIST) < 10:  # 低负载
                        sleep_time *= 2
                    time.sleep(sleep_time)
            else:
                # 处理剩余批量任务
                if local_batch:
                    batch_process_users(local_batch)
                    local_batch.clear()

                logger.info(f"工作线程 [{worker_id}]：当前无有效用户，等待 5 秒...")
                time.sleep(5)

        except Exception as e:
            logger.error(f"工作线程 [{worker_id}] 发生未知错误: {e}", exc_info=True)
            # 清理本地批量，避免内存泄漏
            local_batch.clear()
            time.sleep(5)

# --- 主程序入口 ---


def performance_monitor():
    """🚀 性能监控线程"""
    while True:
        try:
            time.sleep(300)  # 每5分钟监控一次

            # 获取性能统计
            stats = performance_optimizer.request_stats
            session_stats = {
                'session_pool_size': len(performance_optimizer.session_manager.sessions),
                'total_session_usage': sum(performance_optimizer.session_manager.session_usage.values())
            }

            # 记录性能指标
            for operation, data in stats.items():
                if data['total'] > 0:
                    success_rate = (data['success'] / data['total']) * 100
                    avg_time = data['total_time'] / data['total']
                    logger.info(f"📊 实时监控 - {operation}: 成功率{success_rate:.1f}%, 平均耗时{avg_time:.2f}s, 总请求{data['total']}")

            logger.info(f"📊 会话统计: 会话池{session_stats['session_pool_size']}, 总使用次数{session_stats['total_session_usage']}")

            # 🎯 实时模式提示
            logger.info("🔄 实时监控模式: 无缓存，确保数据实时性")

        except Exception as e:
            logger.error(f"性能监控线程出错: {e}")


def graceful_shutdown():
    """🚀 优雅关闭"""
    logger.info("🔄 开始优雅关闭...")

    # 关闭性能优化器
    performance_optimizer.cleanup_sessions()

    # 关闭并发管理器
    concurrent_manager.shutdown()

    logger.info("✅ 优雅关闭完成")


def main():
    if not redis_client.client:
        logger.error("Redis 未连接，程序无法启动。")
        return

    logger.info("🚀 启动高性能美国签证预约系统...")

    # 启动后台线程
    threading.Thread(target=refresh_user_list_periodically, daemon=True).start()
    logger.info("✅ 用户列表刷新线程已启动")

    threading.Thread(target=clear_reported_dates_periodically, daemon=True).start()
    logger.info("✅ 已报告日期清空线程已启动")

    threading.Thread(target=dynamic_worker_adjuster, daemon=True).start()
    logger.info("✅ 动态线程数调整线程已启动")

    # 🚀 启动性能监控线程
    threading.Thread(target=performance_monitor, daemon=True).start()
    logger.info("✅ 性能监控线程已启动")

    logger.info("⏳ 等待首次用户列表加载...")
    time.sleep(5)

    # 启动所有工作线程（峰值数量）
    for i in range(PEAK_WORKERS):
        thread = threading.Thread(target=worker, args=(i,), daemon=True)
        thread.start()
        if i < NORMAL_WORKERS:
            logger.info(f"✅ 核心工作线程 {i+1}/{NORMAL_WORKERS} 已启动")
        else:
            logger.info(f"⏸️ 峰值预备线程 {i+1}/{PEAK_WORKERS} 已启动并待命")

    logger.info("🎯 高性能预约系统启动完成！")

    try:
        while True:
            time.sleep(3600)
            with ACTIVE_WORKERS_LOCK:
                current_active = ACTIVE_WORKERS

            # 🎯 实时监控状态报告
            session_count = len(performance_optimizer.session_manager.sessions)
            total_requests = sum(data['total'] for data in performance_optimizer.request_stats.values())

            logger.info(f"📊 实时系统状态: 有效用户{len(VALID_USER_LIST)}, 活跃线程{current_active}, "
                        f"会话池{session_count}, 总请求{total_requests} [实时模式-无缓存]")

    except KeyboardInterrupt:
        logger.info("🛑 接收到退出信号，开始优雅关闭...")
        graceful_shutdown()


if __name__ == "__main__":
    main()
