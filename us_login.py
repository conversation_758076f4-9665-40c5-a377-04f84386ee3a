from curl_cffi import requests
import random
import requests as req
from loguru import logger
from bs4 import BeautifulSoup
import json
import time
import base64
import re
from urllib.parse import urlencode
import threading
from RedisClientUS import redis_client
from queue import Queue, Empty
from datetime import datetime

import ddddocr

ocr = ddddocr.DdddOcr(det=False, ocr=False, import_onnx_path="ocr/us.onnx", charsets_path="ocr/charsets.json")


# 最大重试次数
MAX_RETRIES = 0

PROXIES = {
    "http": "http://kq123456-zone-resi:<EMAIL>:16666",
    "https": "http://kq123456-zone-resi:<EMAIL>:16666"
    # "http": "http://scrapeops:<EMAIL>:8181",
    # "https": "http://scrapeops:<EMAIL>:8181"
}
BASE_LOGIN_URL = "https://atlasauth.b2clogin.com/f50ebcfb-eadd-41d8-9099-a7049d073f5c/B2C_1A_atoproduction_Atlas_SUSI"
CAPTCHA_URL = "https://captcha.cgiatlas.com/atlas-captcha-svcs/v1/captcha"
YESCAPTCHA_CREATE_URL = "https://cn.yescaptcha.com/createTask"
YESCAPTCHA_RESULT_URL = "https://cn.yescaptcha.com/getTaskResult"
USVISA_BASE_URL = "https://www.usvisascheduling.com"

# 请将此key替换为您的实际YESCAPTCHA KEY
YESCAPTCHA_API_KEY = "5eb8d30d05a90b906062c792b171b87ef636be9226751"

# redis_client = RedisClient()


def mission_code_to_chn(code):
    if code == "us":
        return "美国"


def center_code_to_chn(code):
    mapping = {
        "BEIJING": "北京",
        "GUANGZHOU": "广州",
        "SHANGHAI": "上海",
        "CEHNGDU": "成都",
        "WUHAN": "武汉",
        "SHENGYANG": "沈阳",
    }
    return mapping.get(code)


def extract_settings(html_content):
    pattern = r'var SETTINGS = (.*?)};'
    match = re.search(pattern, html_content, re.DOTALL)
    if match:
        json_str = match.group(1).strip() + '}'
        return json.loads(json_str)
    else:
        return None


def get_session():
    s = requests.Session()
    return s


def yescaptcha_solve(img_data: bytes, api_key: str):
    img_base64 = base64.b64encode(img_data).decode('utf-8')
    payload = {
        "clientKey": api_key,
        "task": {
            "type": "ImageToTextTaskM1",
            "body": img_base64
        }
    }

    task_response = req.post(YESCAPTCHA_CREATE_URL, json=payload)
    task_data = task_response.json()
    task_id = task_data.get("taskId")
    if not task_id:
        raise Exception(f"创建识别任务失败: {task_data}")

    result_payload = {
        "clientKey": api_key,
        "taskId": task_id
    }

    while True:
        result_response = req.post(YESCAPTCHA_RESULT_URL, json=result_payload)
        result_data = result_response.json()
        status = result_data.get("status")

        if status == "ready":
            solution = result_data.get("solution", {}).get("text")
            return solution
        elif status == "processing":
            time.sleep(1)
        else:
            raise Exception(f"验证码识别返回异常状态: {result_data}")


def get_captcha_info():
    try:
        timestamp = int(time.time() * 1000)
        url = f"{CAPTCHA_URL}?{timestamp}"
        response = req.get(url)
        if response.status_code != 200:
            raise Exception("获取验证码失败")
    except Exception as e:
        logger.error(f"获取验证码请求异常: {e}")
        return None, None, None

    c_token = response.headers.get("C-Token")
    img_bytes = response.content
    # captcha_text = yescaptcha_solve(img_bytes, api_key=YESCAPTCHA_API_KEY)
    captcha_text = ocr.classification(img_bytes)
    return captcha_text, c_token, img_bytes


def get_login_page(session):
    logger.debug("获取登录页面")
    url = f"{USVISA_BASE_URL}/zh-CN/SignIn"
    try:
        response = session.get(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
            proxies=PROXIES,
            timeout=30,
            verify=False,
        )
    except Exception as e:
        logger.error(f"获取登录页面请求异常: {e}")
        return None

    if response.status_code != 200:
        logger.error(f"获取登录页面失败,状态码:{response.status_code}")
        return None

    settings = extract_settings(response.text)
    if not settings:
        logger.error("未找到SETTINGS变量")
        return None
    print(settings)
    return settings


def login_step1(session, user, settings, captcha_text, c_token, image_data, retry=False):
    logger.debug(f"登录步骤1:{settings}, {captcha_text} {c_token}")
    cookies = session.cookies.get_dict()
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "X-CSRF-TOKEN": cookies.get('x-ms-cpim-csrf')
    }
    data = {
        "signInName": user.get('username'),
        "password": user.get('password'),
        "extension_atlasCaptchaResponse": captcha_text,
        "extension_atlasCaptchaToken": c_token,
        "request_type": "RESPONSE"
    }

    url = f"{BASE_LOGIN_URL}/SelfAsserted?tx={settings.get('transId')}&p=B2C_1A_atoproduction_Atlas_SUSI"
    response = session.post(
        url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        headers=headers,
        data=data,
        proxies=PROXIES,
        verify=False,
    )

    if response.status_code == 200:
        res_json = response.json()
        status_code = res_json.get('status')
        if status_code == "200":
            # with open(f"images/{captcha_text}.png", "wb") as f:
            #     f.write(image_data)
            return True
        elif status_code == "400" and not retry:
            logger.info("登录返回status为400，重新获取验证码并重试")
            new_captcha_text, new_c_token, new_image_data = get_captcha_info()
            if not new_captcha_text:
                logger.error("重新获取验证码失败")
                return False
            return login_step1(session, user, settings, new_captcha_text, new_c_token, new_image_data, retry=True)
        else:
            logger.warning(f"登录请求返回非预期状态, status: {status_code}")
    else:
        logger.warning(f"发送登录请求失败,{response.status_code}")

    return False


def login_step2(session, settings):
    logger.debug("登录步骤2")
    timestamp = int(time.time())
    trace_data = {
        'pageViewId': settings.get('pageViewId'),
        'pageId': 'SelfAsserted',
        'trace': [
            {'ac': 'T005', 'acST': timestamp, 'acD': 3},
        ]
    }
    trace = json.dumps(trace_data)
    url = f"{BASE_LOGIN_URL}/api/SelfAsserted/confirmed?csrf_token={settings.get('csrf')}&tx={settings.get('transId')}&p=B2C_1A_atoproduction_Atlas_SUSI&diags={trace}"
    response = session.get(
        url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        verify=False,
    )
    if response.status_code != 200:
        logger.warning(f"发送登录2请求失败,{response.status_code}")
        return None, None

    html_content = response.text
    settings_match = re.search(r'var SETTINGS = (.*?)};', html_content, re.DOTALL)
    sa_match = re.search(r'var SA_FIELDS = (.*?)};', html_content, re.DOTALL)
    if not (settings_match and sa_match):
        logger.error("未找到SETTINGS或SA_FIELDS变量")
        return None, None

    new_settings_str = settings_match.group(1).strip() + '}'
    new_settings = json.loads(new_settings_str)

    sa_str = sa_match.group(1).strip() + '}'
    sa_fields = json.loads(sa_str)

    return new_settings, sa_fields


def login_step3(session, settings, data):
    logger.debug("登录步骤3")
    cookies = session.cookies.get_dict()
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "X-CSRF-TOKEN": cookies.get('x-ms-cpim-csrf')
    }

    url = f"{BASE_LOGIN_URL}/SelfAsserted?tx={settings.get('transId')}&p=B2C_1A_atoproduction_Atlas_SUSI"
    response = session.post(
        url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        headers=headers,
        data=data,
        verify=False,
    )

    if response.status_code == 200:
        res_json = response.json()
        if res_json.get('status') == "200":
            logger.debug("步骤3登录成功")
            return True
    logger.warning(f"步骤3登录请求失败,{response.status_code}")
    return False


def login_step4(session, settings):
    logger.debug("登录步骤4")
    timestamp = int(time.time())
    trace_data = {
        'pageViewId': settings.get('pageViewId'),
        'pageId': 'SelfAsserted',
        'trace': [{'ac': 'T005', 'acST': timestamp, 'acD': 3}]
    }
    trace = json.dumps(trace_data)
    url = f"{BASE_LOGIN_URL}/api/SelfAsserted/confirmed?csrf_token={settings.get('csrf')}&tx={settings.get('transId')}&p=B2C_1A_atoproduction_Atlas_SUSI&diags={trace}"
    response = session.get(
        url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        verify=False,
    )
    if response.status_code != 200:
        logger.warning(f"发送登录4请求失败,{response.status_code}")
        return None

    soup = BeautifulSoup(response.text, 'html.parser')
    inputs = soup.find_all('input')
    form_data = {inp.get('name'): inp.get('value', '') for inp in inputs if inp.get('name')}
    return form_data


def login_step5(session, data, attempt=0, max_attempt=3):
    """
    给第5步加上有限次重试，避免无限递归
    """
    logger.info(f"登录步骤5使用数据：{data}, attempt={attempt}")
    if attempt >= max_attempt:
        logger.warning("登录步骤5失败次数过多，返回 False")
        return False

    try:
        url = f"{USVISA_BASE_URL}/signin-aad-b2c_1"
        headers = {
            "origin": "https://atlasauth.b2clogin.com",
            "Referer": "https://atlasauth.b2clogin.com/",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Dest": "document",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        }

        new_session = requests.Session()
        for name, value in session.cookies.get_dict().items():
            new_session.cookies.set(name, value)
        response = new_session.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
            proxies=PROXIES,
            headers=headers,
            data=data,
            verify=False,
            timeout=40,
        )

        if response.status_code == 200:
            logger.debug("步骤5成功登录USVISA")
            # 同步 Cookie 到原 session
            for name, value in new_session.cookies.get_dict().items():
                session.cookies.set(name, value)
            return True
        else:
            logger.warning(f"步骤5登录请求失败,{response.status_code}")
            return False

    except Exception as e:
        logger.error(f"登录步骤5请求异常: {e}")
        time.sleep(1)
        return login_step5(session, data, attempt=attempt+1, max_attempt=max_attempt)


def login_step6(session):
    logger.debug("登录步骤6")
    url = f"{USVISA_BASE_URL}/zh-CN/"
    response = session.get(
        url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        proxies=PROXIES,
        verify=False,
    )
    if response.status_code != 200:
        logger.warning(f"发送登录6请求失败,{response.status_code}")
        return None

    return response


def fetch_schedule_page(session):
    schedule_url = f"{USVISA_BASE_URL}/zh-CN/schedule/"
    headers_date = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": "https://www.usvisascheduling.com/zh-CN/schedule/",
        "origin": "https://www.usvisascheduling.com",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    }
    resp_schedule = session.get(
        schedule_url,
        headers=headers_date,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        proxies=PROXIES,
        verify=False,
        timeout=120
    )

    if resp_schedule.status_code == 200:
        contact_id_match = re.search(r"contactId:\s*'([^']+)'", resp_schedule.text)
        contact_id = contact_id_match.group(1) if contact_id_match else None

        pattern = r"jsdata\['([^']+)'\]\s*=\s*(.*?);"
        matches = re.findall(pattern, resp_schedule.text)
        result = {}
        for key, value_str in matches:
            if key not in result:
                value_str = value_str.strip()
                if value_str.startswith('"') and value_str.endswith('"'):
                    value = value_str.strip('"')
                elif value_str == '[]':
                    value = []
                else:
                    value = value_str
                result[key] = value

        return result, contact_id
    else:
        logger.warning(f"schedule请求失败,{resp_schedule.status_code}")
        return None, None


def fetch_post_id(session, contact_id, primaryId):
    headers_date = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": "https://www.usvisascheduling.com/zh-CN/schedule/",
        "origin": "https://www.usvisascheduling.com",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    }

    members_url = f"{USVISA_BASE_URL}/zh-CN/custom-actions/?route=/api/v1/schedule-group/query-consular-posts&appd={contact_id}&cacheString={int(time.time() * 1000)}"
    params = {
        "parameters": json.dumps({
            "applicationId": primaryId,
        })
    }
    resp_schedule = session.post(
        members_url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        proxies=PROXIES,
        headers=headers_date,
        verify=False,
        data=params,
        timeout=20
    )

    if resp_schedule.status_code == 200:
        logger.debug(f"获取members成功,{resp_schedule.json()}")
        return resp_schedule.json().get('Posts')[0].get('ID')
    else:
        logger.warning(f"schedule请求失败,{resp_schedule.status_code}")
        return None


def fetch_members(session, contact_id, primaryId):
    headers_date = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": "https://www.usvisascheduling.com/zh-CN/schedule/",
        "origin": "https://www.usvisascheduling.com",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    }

    members_url = f"{USVISA_BASE_URL}/zh-CN/custom-actions/?route=/api/v1/schedule-group/query-family-members-consular&appd={contact_id}&cacheString={int(time.time() * 1000)}"
    params = {
        "parameters": json.dumps({
            "primaryId": primaryId,
            "visaClass": "all",
        })
    }
    resp_schedule = session.post(
        members_url,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        proxies=PROXIES,
        headers=headers_date,
        verify=False,
        data=params,
        timeout=20
    )

    if resp_schedule.status_code == 200:
        logger.debug(f"获取members成功,{resp_schedule.json()}")
        return resp_schedule.json().get('Members')
    else:
        logger.warning(f"schedule请求失败,{resp_schedule.status_code}")
        return None, None


def fetch_members_reschedule(session, contact_id, primaryId):
    headers_date = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": "https://www.usvisascheduling.com/zh-CN/schedule/",
        "origin": "https://www.usvisascheduling.com",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    }
    members_url = f"{USVISA_BASE_URL}/zh-CN/custom-actions/?route=/api/v1/schedule-group/query-family-members-consular-reschedule&appd={contact_id}&cacheString={int(time.time() * 1000)}"
    params = {
        "parameters": json.dumps({
            "primaryId": primaryId,
            "visaClass": "all",
        })
    }
    resp_schedule = session.post(
        members_url,
        headers=headers_date,
        impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios']),
        proxies=PROXIES,
        verify=False,
        data=params,
        timeout=20
    )

    if resp_schedule.status_code == 200:
        logger.debug(f"获取members成功,{resp_schedule.json()}")
        return resp_schedule.json().get('Members')
    else:
        logger.warning(f"schedule请求失败,{resp_schedule.status_code}")
        return None, None


def attempt_login_step5(session, step4_response, max_retries=3, retry_interval=0):
    for _ in range(max_retries):
        result = login_step5(session, step4_response)
        if result:
            return result
        time.sleep(retry_interval)
    return False


def get_token(user):
    """
    你的核心登录逻辑。
    返回 True 表示成功登录, 返回 False 表示失败 (需要重试)。
    """
    try:
        session = get_session()
        settings = get_login_page(session)
        if not settings:
            logger.error(f"{user.get('username')} 初始化获取settings失败")
            return False

        captcha_text, c_token, image_data = get_captcha_info()
        if not captcha_text:
            logger.error(f"{user.get('username')} 获取验证码失败")
            return False

        success = login_step1(session, user, settings, captcha_text, c_token, image_data)
        if not success:
            logger.error(f"{user.get('username')} 登录步骤1失败")
            return False

        new_settings, sa = login_step2(session, settings)
        if not new_settings or not sa:
            logger.error(f"{user.get('username')} 登录步骤2失败")
            return False

        # 如果有安全问题字段
        if sa.get('AttributeFields'):
            question_data = {
                "signInName": user.get('username'),
                "request_type": "RESPONSE"
            }
            # (此处的安全问题解析逻辑省略, 保持原代码)
            for item in sa['AttributeFields']:
                dn_value = item.get('DN', '')
                if 'Security Question' in dn_value or '安全性问题' in dn_value:
                    maped_question = item.get('PRE')
                    if maped_question == "What is your mother&#39;s maiden name?":
                        maped_question = "您母亲的姓氏是什么?"
                    if maped_question == "What was the name of your first/current/favorite pet?":
                        maped_question = "您的第一个/目前/最喜欢的宠物名称是什么?"
                    if maped_question == "What was your first car?":
                        maped_question = "您的第一辆汽车是什么车?"
                    if maped_question == "What elementary school did you attend?":
                        maped_question = "您在哪所小学上学?"
                    if maped_question == "What is the name of the town/city where you were born?":
                        maped_question = "您出生的地方/城市叫什么名字?"
                    if maped_question == "What is the name of the road/street you grew up on?":
                        maped_question = "您成长的道路/街道叫称是什么?"
                    if maped_question == "What is your least favorite food?":
                        maped_question = "您最不喜欢的食物是什么?"
                    if maped_question == "What was the first company that you worked for?":
                        maped_question = "您的第一份工作是在哪家公司?"
                    if maped_question == "What is your favorite food?":
                        maped_question = "您最喜欢的食物是什么?"
                    if maped_question == "What high school did you attend?":
                        maped_question = "您在哪所高中求学?"
                    if maped_question == "Where did you meet your spouse?":
                        maped_question = "您在哪里遇见您的配偶?"
                    if maped_question == "What is your sibling&#39;s middle name?":
                        maped_question = "您的兄弟姐妹的中间名字是什么?"
                    if maped_question == "Who was your childhood hero?":
                        maped_question = "谁是您儿时的英雄?"
                    if maped_question == "In what city or town was your first job?":
                        maped_question = "您的第一份工作在哪个城市或地方?"
                    if maped_question == "What is the name of a college you applied to but didn’t attend?":
                        maped_question = "您申请但未就读的大学是哪所大学?"
                    result = next((aws for aws in user.get('qa', []) if aws["DISP"] == maped_question), None)
                    if result:
                        if "kbq1" in item.get('ID'):
                            question_data['kba1_response'] = result.get("VAL")
                        elif "kbq2" in item.get('ID'):
                            question_data['kba2_response'] = result.get("VAL")
                        elif "kbq3" in item.get('ID'):
                            question_data['kba3_response'] = result.get("VAL")
                    else:
                        logger.warning(f"{user.get('username')} 未找到对应安全问题答案: {maped_question}")

            step3_success = login_step3(session, new_settings, question_data)
            if not step3_success:
                logger.error(f"{user.get('username')} 登录步骤3失败")
                return False

            step4_response = login_step4(session, new_settings)
            if not step4_response:
                logger.error(f"{user.get('username')} 登录步骤4失败")
                return False

            step5_success = attempt_login_step5(session, step4_response)
            if not step5_success:
                logger.error(f"{user.get('username')} 登录步骤5失败")
                return False

            step6_response = login_step6(session)
            if not step6_response:
                logger.error(f"{user.get('username')} 登录步骤6失败")
                return False
            soup = BeautifulSoup(step6_response.text, 'html.parser')
            # 找到包含 "Visa Information" 文字的 <span> 标签
            visa_info_span = soup.find('span', string="Visa Information")

            if visa_info_span:
                # 找到包含该 <span> 的父级 <div>
                parent_div = visa_info_span.find_parent('div')

                if parent_div:
                    # 在父级 <div> 里查找 <p> 标签
                    p_tag = parent_div.find('p')

                    if p_tag:
                        read_centerCode = p_tag.get_text(separator=" ", strip=True).split(' ')[0]
                        user['centerCode'] = read_centerCode
                        redis_client.hset('usUserDatas', user.get('username'), json.dumps(user))

                        lines = [x.strip() for x in p_tag.stripped_strings]
                        if len(lines) >= 3:
                            visa_type = lines[1]  # 第二段文字，即两个 <br /> 中间的内容
                            print(visa_type)
                            user['visa_type'] = visa_type
                    else:
                        print("未找到 <p> 标签")
                else:
                    print("未找到父级 <div>")

            pattern = re.compile(r"moment\('([^']+)'\)")
            match = pattern.search(step6_response.text)
            if match:
                date_str = match.group(1)
                # 这里 date_str 就是 "1/15/25 08:00"

                # 2. 将字符串转换为 datetime 对象
                #    根据"1/15/25 08:00" 这一格式，采用 '%m/%d/%y %H:%M'
                dt = datetime.strptime(date_str, '%m/%d/%y %H:%M')

                # 例如输出成更易读的格式：YYYY-MM-DD HH:MM:SS
                print(f"客户{user.get('username')}, 已预约日期: ", dt.strftime('%Y-%m-%d %H:%M:%S'))
                user["scheduled_date"] = dt.strftime('%Y-%m-%d %H:%M:%S')

                # if user['start_date'] <= user["scheduled_date"] <= user['end_date']:
                #     if "蔡徐坤" not in user.get('name'):
                #         url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066"
                #         postData = {
                #             "msgtype": "text",
                #             "text": {
                #                 "content": f"美国{center_code_to_chn(user.get('centerCode'))}客户: {user.get('name')} 已有匹配的日期， 预约日期 : {user['scheduled_date']}, 备注: {user.get('remark')} , 请登录网页下载预约信，用户名：{user.get('username')}，密码：{user.get('password')}，问题：{user.get('qa')}",
                #             },
                #         }
                #         requests.post(url, json=postData)
                #         redis_client.hset('successUserDatas', user.get('username'), json.dumps({**user, "scheduled_date": f"{user['scheduled_date']}", "update_order_time": int(time.time()), "scheduled": True}))
                #     else:
                #         url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3d011be5-7ec3-4709-952a-e8ea50f89249"
                #         postData = {
                #             "msgtype": "text",
                #             "text": {
                #                 "content": f"美国{center_code_to_chn(user.get('centerCode'))}客户: {user.get('name')} 已有匹配的日期， 预约日期 : {user['scheduled_date']}, 备注: {user.get('remark')} , 请登录网页下载预约信，用户名：{user.get('username')}，密码：{user.get('password')}，问题：{user.get('qa')}",
                #             },
                #         }
                #         requests.post(url, json=postData)
                #     redis_client.hdel('usUserDatas', user.get('username'))
                #     return True

            else:
                user["scheduled_date"] = None
                print(f"客户{user.get('username')}, 未预约")

            # 如果没有 primaryId，就去拉取
            if not user.get('primaryId') or not user.get('post_id') or not user.get('contact_id'):
                schedule_data, contact_id = fetch_schedule_page(session)
                logger.info(f"{user.get('username')} 获取Schedule页面数据, {schedule_data},{contact_id}")
                if not schedule_data:
                    logger.warning(f"{user.get('username')} 获取Schedule页面数据失败")
                    return False
                primary_id = schedule_data.get('primaryId')
                post_id = fetch_post_id(session, contact_id, primary_id)
                user['contact_id'] = contact_id
                user['primaryId'] = primary_id
                user['post_id'] = post_id
            else:
                primary_id = user.get('primaryId')
                post_id = user.get('post_id')
                contact_id = user.get('contact_id')
            if user.get('members') == None:
                if not primary_id or not post_id:
                    logger.warning(f"{user.get('username')} primaryId或postId不存在")
                    return False
                if not user.get("scheduled_date"):
                    members = fetch_members(session, contact_id, primary_id)
                else:
                    members = fetch_members_reschedule(session, contact_id, primary_id)
                if len(members) == 0:
                    user['members'] = [{"ApplicationID": primary_id, }]
                    logger.info(f"{user.get('username')} 设置默认Members")
                elif not members[0]:
                    logger.warning(f"{user.get('username')} 获取members页面数据失败 {members}")
                    return False
                else:
                    logger.info(f"{user.get('username')} 获取Members, {members}")
                    user['members'] = members

            # 存储 cookie 以及 last_login
            user['cookie'] = session.cookies.get_dict()
            user['last_login'] = int(time.time())
            if redis_client.hget('usUserDatas', user.get('username')):
                redis_client.hset(
                    'usUserDatas',
                    user.get('username'),
                    json.dumps({**user})
                )
            return True

        else:
            # 这里视情况而定
            logger.error(f"{user.get('username')} 不存在安全问题字段？请检查逻辑")
            return False

    except Exception as e:
        logger.error(f"{user.get('username')} 登录失败(异常), {e}")
        return False


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def getNeedProceed(user_list):
    current_time = int(time.time())

    def filter_condition(user):
        return not user.get("last_login") or current_time - user.get("last_login",0) > 200
        

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed

# ------------------ 新增: 用 Queue + 多线程 实现失败重试 ------------------


def run_users_queue(need_proceed, max_workers=50):
    """
    1. 把所有待登录的用户放进队列
    2. 启动 max_workers 条线程, 每条线程循环从队列里拿用户
       调用 get_token(user)，若失败则检查重试次数
       未超过 MAX_RETRIES 就放回队列, 否则放弃
    3. 当队列空了或者全部用户都处理完/放弃后, 主线程退出
    """

    # 给每个 user 一个重试计数器, 不放在全局
    user_queue = Queue()
    for user in need_proceed:
        # 如果没有 'retries', 置为 0
        user['retries'] = 0
        user_queue.put(user)

    def worker():
        while True:
            try:
                current_user = user_queue.get(block=False)
            except Empty:
                break  # 队列空, 退出循环

            username = current_user.get('username')
            logger.info(f"开始处理 {username}, 已重试 {current_user['retries']} 次")
            success = get_token(current_user)
            if not success:
                current_user['retries'] += 1
                if current_user['retries'] <= MAX_RETRIES:
                    logger.warning(f"[{username}] 登录失败, 即将第 {current_user['retries']} 次重试")
                    time.sleep(0.5)  # 可适当 sleep
                    user_queue.put(current_user)
                else:
                    logger.warning(f"[{username}] 超过最大重试次数, 放弃")
            user_queue.task_done()

    # 启动多条线程
    threads = []
    for _ in range(max_workers):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)

    # 等待队列里的所有任务被处理完
    user_queue.join()
    # 此时所有任务都被处理(成功或放弃), 通知线程退出
    for t in threads:
        t.join()

    logger.info("所有用户都处理完成(成功或放弃)。")


# ------------------ 主循环逻辑 (保持原有结构) ------------------
if __name__ == "__main__":
    while True:
        loginUsers = redis_client.hgetall("usUserDatas")
        need_proceed = getNeedProceed(loginUsers)

        if len(need_proceed) != 0:
            print(f"正在刷新 {len(need_proceed)} 个账号票据")
            # 改为用队列并发处理
            run_users_queue(need_proceed, max_workers=50)
        else:
            print("暂无需要刷新的票据")
            time.sleep(1)
