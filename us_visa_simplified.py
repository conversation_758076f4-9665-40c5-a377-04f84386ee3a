import random
import time
import json
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import wraps

# 第三方库
from curl_cffi import requests
from tools.logger import logger
from RedisClientUS import redis_client

# --- 配置参数 ---
THREAD_POOL_SIZE = 10  # 线程池大小
USER_FETCH_INTERVAL = 5  # 用户获取间隔
SESSION_VALID_DURATION = 2000  # 会话有效期
EARLY_DATE_THRESHOLD = "2025-08-30"  # 早期日期阈值

# API配置
USVISA_BASE_URL = "https://www.usvisascheduling.com"
DEFAULT_PROXIES = {"http": "http://kq123456-zone-resi:<EMAIL>:16666", "https": "http://kq123456-zone-resi:<EMAIL>:16666"}
BOOKING_PROXIES = {
    "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-us:<EMAIL>:33335",
    "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-us:<EMAIL>:33335",
}

# 微信通知URL
WECHAT_BOT_URL_SCHEDULE = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066"
WECHAT_BOT_URL_NORMAL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c5539dd1-a547-4225-ab10-bb0e871af9a"

# 全局变量
VALID_USER_LIST = []
USER_LIST_LOCK = threading.Lock()
IMPERSONATE_VERSIONS = ["chrome136", "safari17_0", "safari18_4", "safari18_0", "edge101", "tor145"]

# 线程管理相关
USER_GROUPS = {}  # 存储按类型分组的用户
USER_GROUPS_LOCK = threading.Lock()
THREAD_EXECUTORS = {}  # 存储每个类型对应的线程执行器


# --- 工具函数 ---
def retry_on_failure(retries=2, delay=1):
    """重试装饰器"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for i in range(retries):
                try:
                    result = func(*args, **kwargs)
                    if result is not None:
                        return result
                    logger.warning(f"函数 {func.__name__} 返回 None，将在 {delay} 秒后重试...")
                except Exception as e:
                    logger.warning(f"函数 {func.__name__} 第 {i + 1}/{retries} 次尝试失败: {e}")
                time.sleep(delay)
            logger.error(f"函数 {func.__name__} 在尝试 {retries} 次后彻底失败")
            return None

        return wrapper

    return decorator


def center_code_to_chn(code):
    """领事馆代码转中文"""
    mapping = {"BEIJING": "北京", "GUANGZHOU": "广州", "SHANGHAI": "上海", "CHENGDU": "成都", "WUHAN": "武汉", "SHENYANG": "沈阳"}
    return mapping.get(code, code)


def notify_wechat(message, is_success=False):
    """发送微信通知"""
    url = WECHAT_BOT_URL_SCHEDULE if is_success else WECHAT_BOT_URL_NORMAL
    try:
        post_data = {"msgtype": "text", "text": {"content": message}}
        requests.post(url, json=post_data, timeout=10)
        logger.info(f"微信通知发送成功: {message[:50]}...")
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {e}")


# --- API调用函数 ---


@retry_on_failure()
def make_api_call(user, endpoint, params, timeout=120, proxies=DEFAULT_PROXIES):
    """统一API调用函数"""
    username = user.get("username")
    timestamp = int(time.time() * 1000)
    url = f"{USVISA_BASE_URL}/zh-CN/custom-actions/?route={endpoint}&appd={user.get('contact_id')}&cacheString={timestamp}&_t={timestamp}"

    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": f"{USVISA_BASE_URL}/zh-CN/schedule/",
        "origin": USVISA_BASE_URL,
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
    }

    cookie = user.get("cookie", {})
    if "__cf_bm" in cookie:
        del cookie["__cf_bm"]

    try:
        response = requests.post(url, impersonate=random.choice(IMPERSONATE_VERSIONS), headers=headers, data={"parameters": json.dumps(params)}, proxies=proxies, verify=False, cookies=cookie, timeout=timeout)

        if "Unauthorized" in response.text or response.status_code == 401:
            logger.warning(f"用户 [{username}] 认证失败，标记为需要重新登录")
            user["last_login"] = int(time.time() - SESSION_VALID_DURATION * 2)
            redis_client.hset("usUserDatas", username, json.dumps(user))
            return None

        response.raise_for_status()
        return response.json()

    except Exception as e:
        logger.error(f"API调用失败 - 用户: {username}, 端点: {endpoint}, 错误: {e}")
        raise e


def get_user_schedule(user):
    if user.get("scheduled_date", None):
        return "false"
    return "true"


def fetch_available_days(user):
    """获取可用日期"""
    logger.info(f"获取可用日期 - 用户 [{user.get('username')}]")
    params = {
        "primaryId": user.get("primaryId"),
        "applications": [user.get("primaryId")],
        "scheduleDayId": "",
        "scheduleEntryId": "",
        "postId": user.get("post_id"),
        "isReschedule": get_user_schedule(user),
    }
    return make_api_call(user, "/api/v1/schedule-group/get-family-consular-schedule-days", params)


def fetch_available_times(user, day_info, token):
    """获取可用时间段"""
    logger.info(f"获取时间段 - 用户 [{user.get('username')}] - 日期 {day_info.get('Date')}")
    params = {
        "primaryId": None,
        "applications": None,
        "scheduleDayId": None,
        "scheduleEntryId": "",
        "postId": None,
        "Token": token,
        "Date": f"{day_info.get('Date')}T00:00:00.000Z",
        "isReschedule": get_user_schedule(user),
    }
    return make_api_call(user, "/api/v1/schedule-group/get-family-consular-schedule-entries", params, proxies=BOOKING_PROXIES)


def book_appointment(user, day_info, token, num_of_times):
    """预约操作"""
    logger.info(f"尝试预约 - 用户 [{user.get('username')}] - 日期 {day_info.get('Date')}")
    # TODO: 实现预约逻辑
    return None

    members = user.get("members", [])
    params = {
        "primaryId": None,
        "applications": [item.get("ApplicationID") for item in members] if len(members) > 1 else None,
        "scheduleDayId": None,
        "scheduleEntryId": "",
        "postId": None,
        "Token": token,
        "Date": f"{day_info.get('Date')}T00:00:00.000Z",
        "Num": str(random.randint(1, num_of_times)),
    }
    endpoint = "/api/v1/schedule-group/reschedule-consular-appointments-for-family" if user.get("scheduled_date") else "/api/v1/schedule-group/schedule-consular-appointments-for-family"
    return make_api_call(user, endpoint, params, timeout=120, proxies=BOOKING_PROXIES)


# --- 核心业务逻辑 ---


def check_early_dates(center_code, center_name, all_days, username):
    """检查并通知早期日期"""
    early_dates = [day.get("Date")[:10] for day in all_days if day.get("Date", "")[:10] < EARLY_DATE_THRESHOLD]

    if early_dates:
        message = f"🚨【{center_name}早期日期】\n账号: {username}\n发现 {len(early_dates)} 个早于 {EARLY_DATE_THRESHOLD} 的日期:\n{', '.join(sorted(early_dates)[:5])}"
        if len(early_dates) > 5:
            message += f" 等共{len(early_dates)}个日期"
        message += f"\n最早日期: {min(early_dates)}"

        logger.success(message)
        notify_wechat(message)


def collect_all_date_times(user, all_days, token):
    """收集所有日期的时间段信息"""
    username = user.get("username")
    center_code = user.get("centerCode")
    visa_type = user.get("visa_type")  # 默认签证类型
    if visa_type in ["B1", "B2", "B1/B2"]:
        visa_type = "B1/B2"

    date_times_info = []
    logger.info(f"开始收集用户 [{username}] 所有日期的时间段信息，共 {len(all_days)} 个日期")
    for day_info in all_days:
        date_str = day_info.get("Date")[:10]
        try:
            # 获取该日期的时间段
            times_data = fetch_available_times(user, day_info, token)

            if times_data and times_data.get("ScheduleEntries"):
                available_times = times_data.get("ScheduleEntries")

                # 提取时间段信息
                time_slots = []
                for time_entry in available_times:
                    time_slots.append({"time": time_entry.get("Time", ""), "EntriesAvailable": time_entry.get("EntriesAvailable", 0), "Num": time_entry.get("Num", "")})

                date_times_info.append({"date": date_str, "time_slots": time_slots, "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")})

                # logger.info(f"日期 {date_str} 获取到 {len(time_slots)} 个时间段")
            else:
                # logger.warning(f"日期 {date_str} 无可用时间段")
                date_times_info.append({"date": date_str, "time_slots": [], "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")})

            # 添加小延迟避免请求过于频繁
            time.sleep(0.5)

        except Exception as e:
            logger.error(f"获取日期 {date_str} 时间段时出错: {e}")
            continue

    # 保存到Redis
    redis_key = f"us_{center_code}_{visa_type}"
    try:
        redis_client.hset(
            "usDateInfos",
            redis_key,
            json.dumps(
                {"center_code": center_code, "visa_type": visa_type, "dates": date_times_info, "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
                ensure_ascii=False,
            ),
        )
        logger.success(f"已保存用户 [{username}] 的日期时间段信息到Redis，key: {redis_key}")
    except Exception as e:
        logger.error(f"保存日期时间段信息到Redis失败: {e}")

    return date_times_info


def process_single_user(user):
    """单线程处理单个用户的预约流程"""
    username = user.get("username")
    center_code = user.get("centerCode")
    center_name = center_code_to_chn(center_code)

    logger.info(f"开始处理用户 [{username}] 在 [{center_name}] 的预约")

    try:
        # 1. 获取可用日期
        days_data = fetch_available_days(user)
        if not days_data or not days_data.get("ScheduleDays"):
            logger.info(f"用户 [{username}] 在 [{center_name}] 无可用日期")
            return False

        all_days = days_data.get("ScheduleDays", [])
        logger.success(f"用户 [{username}] 发现 {len(all_days)} 个可用日期")

        # 2. 检查早期日期并通知
        check_early_dates(center_code, center_name, all_days, username)

        if not user.get("autoDate", False):  # 不自动预约的账号查询所有日期
            # 3. 收集所有日期的时间段信息并保存到Redis
            date_times_info = collect_all_date_times(user, all_days, days_data.get("Token"))
            logger.success(f"仅扫号，放号日期：{date_times_info}")
            return True
        return True
        # 4. 筛选符合条件的日期
        suitable_days = [day for day in all_days if user.get("start_date", "1970-01-01") <= day.get("Date")[:10] <= user.get("end_date", "2999-12-31")]

        if not suitable_days:
            logger.info(f"用户 [{username}] 的日期范围与可用日期不匹配")
            return False

        # 5. 从有时间段的日期中随机选择一个
        suitable_days_with_times = [day for day in suitable_days if date_times_info.get(day.get("Date")[:10], {}).get("total_slots", 0) > 0]

        if not suitable_days_with_times:
            logger.info(f"用户 [{username}] 的符合条件日期都没有可用时间段")
            return False

        selected_day = random.choice(suitable_days_with_times)
        logger.info(f"为用户 [{username}] 选中日期: {selected_day.get('Date')}")

        # 6. 获取选中日期的时间段数量
        selected_date_str = selected_day.get("Date")[:10]
        available_time_count = date_times_info.get(selected_date_str, {}).get("total_slots", 0)

        if available_time_count == 0:
            logger.warning(f"用户 [{username}] 选中日期 {selected_date_str} 无时间段")
            return False

        logger.success(f"用户 [{username}] 选中日期有 {available_time_count} 个时间段")

        # 7. 尝试预约
        booking_result = book_appointment(user, selected_day, days_data.get("Token"), available_time_count)

        if booking_result and not booking_result.get("HasError"):
            # 预约成功
            success_message = f"🎉【预约成功】客户: {user.get('name')} ({center_name})\n" f"预约日期: {selected_day.get('Date')[:10]}\n" f"用户名: {username}\n" f"备注: {user.get('remark', '无')}\n" "请及时登录网页下载预约信息。"
            logger.success(f"用户 [{username}] 预约成功！")
            notify_wechat(success_message, is_success=True)

            # 保存成功记录并删除原记录
            redis_client.hset("successUserDatas", f"{center_code}-{username}", json.dumps({**user, "scheduled_date": selected_day.get("Date"), "update_order_time": int(time.time())}))
            redis_client.hdel("usUserDatas", username)
            return True
        else:
            error_msg = booking_result.get("ErrorMessage", "未知错误") if booking_result else "无响应"
            logger.error(f"用户 [{username}] 预约失败: {error_msg}")
            return False

    except Exception as e:
        logger.error(f"处理用户 [{username}] 时发生错误: {e}")
        return False


# --- 用户管理和线程池处理 ---


def refresh_user_list():
    """从Redis刷新用户列表"""
    try:
        all_users_data = redis_client.hgetall("usUserDatas")
        current_time = int(time.time())
        fresh_user_list = []

        for user in all_users_data:
            try:
                # user = json.loads(user_json)
                if user.get("primaryId") and user.get("last_login") and current_time - user.get("last_login") < SESSION_VALID_DURATION:
                    fresh_user_list.append(user)
            except (json.JSONDecodeError, TypeError):
                continue

        with USER_LIST_LOCK:
            global VALID_USER_LIST
            VALID_USER_LIST = fresh_user_list

        logger.info(f"用户列表已更新，当前有效用户数: {len(VALID_USER_LIST)}")
        return len(VALID_USER_LIST)

    except Exception as e:
        logger.error(f"刷新用户列表时发生错误: {e}")
        return 0


def user_refresh_worker():
    """用户列表刷新后台线程"""
    while True:
        refresh_user_list()
        time.sleep(USER_FETCH_INTERVAL)


def main():
    """主程序入口"""
    if not redis_client.client:
        logger.error("Redis 未连接，程序无法启动")
        return

    logger.info("🚀 启动简化版美国签证预约系统...")

    # 启动用户列表刷新线程
    threading.Thread(target=user_refresh_worker, daemon=True).start()

    # 等待首次用户列表加载
    logger.info("✅ 用户列表刷新线程已启动，⏳ 等待首次用户列表加载...")
    time.sleep(5)

    # 使用标准线程池处理用户
    with ThreadPoolExecutor(max_workers=THREAD_POOL_SIZE, thread_name_prefix="UserProcessor") as executor:
        logger.info(f"✅ 线程池已启动，线程数: {THREAD_POOL_SIZE}")

        try:
            round_count = 0
            while True:
                round_count += 1
                logger.info(f"🔄 开始第 {round_count} 轮扫描...")

                # 获取当前用户列表
                with USER_LIST_LOCK:
                    current_users = VALID_USER_LIST.copy()

                if current_users:
                    logger.info(f"📋 本轮处理 {len(current_users)} 个用户")

                    # 提交所有用户到线程池
                    futures = [executor.submit(process_single_user, user) for user in current_users]

                    # 等待所有任务完成
                    success_count = 0
                    completed_count = 0
                    for future in as_completed(futures, timeout=300):  # 5分钟超时
                        try:
                            completed_count += 1
                            if future.result():
                                success_count += 1
                            # 显示进度
                            if completed_count % 5 == 0 or completed_count == len(current_users):
                                logger.info(f"⏳ 进度: {completed_count}/{len(current_users)} 用户已处理")
                        except Exception as e:
                            logger.error(f"处理用户任务时出错: {e}")

                    logger.info(f"✅ 第 {round_count} 轮处理完成，成功: {success_count}/{len(current_users)}")
                else:
                    logger.info("⚠️ 当前无有效用户，等待...")

                # 休眠一段时间再进行下一轮
                logger.info("😴 休眠 10 秒后开始下一轮扫描...")
                time.sleep(10)

        except KeyboardInterrupt:
            logger.info("🛑 接收到退出信号，正在关闭...")
        except Exception as e:
            logger.error(f"主程序发生错误: {e}")


if __name__ == "__main__":
    main()
